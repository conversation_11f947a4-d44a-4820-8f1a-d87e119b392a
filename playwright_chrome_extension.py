#!/usr/bin/env python3
"""
使用 Playwright 打开 Chrome 浏览器并加载 CSFloat Market Checker 插件

使用方法:
1. 确保已安装 playwright: pip install playwright
2. 安装浏览器: playwright install chromium
3. 运行脚本: python playwright_chrome_extension.py
"""

import os
import sys
from pathlib import Path
from playwright.sync_api import sync_playwright


def get_extension_path():
    """获取插件目录的绝对路径"""
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent.absolute()
    # 插件目录路径
    extension_path = script_dir / "dist"
    
    if not extension_path.exists():
        raise FileNotFoundError(f"插件目录不存在: {extension_path}")
    
    # 检查必要的插件文件
    manifest_path = extension_path / "manifest.json"
    if not manifest_path.exists():
        raise FileNotFoundError(f"manifest.json 文件不存在: {manifest_path}")
    
    return str(extension_path)


def launch_browser_with_extension():
    """启动浏览器并加载插件"""
    try:
        extension_path = get_extension_path()
        print(f"插件路径: {extension_path}")
        
        with sync_playwright() as p:
            print("正在启动浏览器...")
            try:
                # 启动 Chrome 浏览器，加载插件
                browser = p.chromium.launch(
                    headless=False,  # 显示浏览器界面
                    args=[
                        f"--load-extension={extension_path}",  # 加载插件
                        "--disable-extensions-except=" + extension_path,  # 只启用指定插件
                        "--disable-web-security",  # 禁用网络安全限制（开发时使用）
                        "--disable-features=VizDisplayCompositor",  # 提高兼容性
                        "--no-sandbox",  # 禁用沙盒模式（某些环境需要）
                    ]
                )
                print("✓ 浏览器启动成功")
            except Exception as browser_error:
                print(f"✗ 浏览器启动失败: {browser_error}")
                if "Executable doesn't exist" in str(browser_error):
                    print("\n解决方案:")
                    print("1. 运行: python fix_playwright.py")
                    print("2. 或手动执行: playwright install")
                raise
            
            # 创建新的浏览器上下文
            context = browser.new_context()
            
            # 创建新页面
            page = context.new_page()
            
            print("浏览器已启动，插件已加载")
            print("插件名称: CSFloat Market Checker")
            print("可以访问以下网站测试插件功能:")
            print("- https://steamcommunity.com/market/listings/730/")
            print("- https://steamcommunity.com/id/[用户名]/inventory/")
            
            # 导航到 Steam 市场页面进行测试
            test_url = "https://steamcommunity.com/market/listings/730/AK-47%20%7C%20Redline%20%28Field-Tested%29"
            print(f"\n正在打开测试页面: {test_url}")
            
            try:
                page.goto(test_url, timeout=30000)
                print("页面加载成功！")
                print("您可以在页面上看到插件的功能效果")
            except Exception as e:
                print(f"页面加载失败: {e}")
                print("您可以手动导航到 Steam 市场页面测试插件")
            
            # 保持浏览器打开，等待用户操作
            print("\n浏览器将保持打开状态...")
            print("按 Ctrl+C 退出程序")
            
            try:
                # 等待用户手动关闭浏览器或按 Ctrl+C
                input("按 Enter 键关闭浏览器...")
            except KeyboardInterrupt:
                print("\n正在关闭浏览器...")
            
            # 关闭浏览器
            browser.close()
            print("浏览器已关闭")
            
    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保在项目根目录运行此脚本，且 dist 目录存在")
        sys.exit(1)
    except Exception as e:
        print(f"启动浏览器时发生错误: {e}")
        print("请检查 Playwright 是否正确安装")
        print("安装命令: pip install playwright && playwright install chromium")
        sys.exit(1)


def check_dependencies():
    """检查依赖是否安装"""
    try:
        import playwright
        print("✓ Playwright 已安装")
    except ImportError:
        print("✗ Playwright 未安装")
        print("请运行: pip install playwright")
        return False
    
    return True


if __name__ == "__main__":
    print("CSFloat Market Checker - Playwright 启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 启动浏览器
    launch_browser_with_extension()
