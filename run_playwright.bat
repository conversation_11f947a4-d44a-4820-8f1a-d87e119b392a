@echo off
chcp 65001 >nul
echo CSFloat Market Checker - Playwright 启动器
echo ================================================

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python 未安装或未添加到 PATH
    echo 请安装 Python 3.8 或更高版本
    pause
    exit /b 1
)

REM 检查插件目录
if not exist "dist" (
    echo 错误: dist 目录不存在
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 检查 Playwright 是否安装
python -c "import playwright" >nul 2>&1
if errorlevel 1 (
    echo Playwright 未安装，正在安装...
    python setup_playwright.py
    if errorlevel 1 (
        echo 安装失败
        pause
        exit /b 1
    )
)

echo.
echo 选择启动模式:
echo 1. 基础模式 (推荐)
echo 2. 高级模式
echo 3. 自动化测试
echo 4. 安装/重新安装 Playwright
echo 5. 快速修复 Playwright
echo.
set /p choice=请输入选择 (1-5):

if "%choice%"=="1" (
    echo 启动基础模式...
    python playwright_chrome_extension.py
) else if "%choice%"=="2" (
    echo 启动高级模式...
    python playwright_advanced.py
) else if "%choice%"=="3" (
    echo 运行自动化测试...
    python playwright_advanced.py --test
) else if "%choice%"=="4" (
    echo 安装 Playwright...
    python setup_playwright.py
) else if "%choice%"=="5" (
    echo 快速修复 Playwright...
    python fix_playwright.py
) else (
    echo 无效选择，启动基础模式...
    python playwright_chrome_extension.py
)

echo.
echo 程序已结束
pause
